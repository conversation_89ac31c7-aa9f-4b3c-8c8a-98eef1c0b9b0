const { GoogleGenerativeAI } = require('@google/generative-ai');

class GeminiService {
  constructor() {
    this.genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
    this.model = this.genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });
  }

  async generateResponse(prompt) {
    try {
      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      return response.text();
    } catch (error) {
      console.error('Error generating response:', error);
      throw new Error('Failed to generate response from Gemini');
    }
  }

  async runPromptChain(input, prompt1, prompt2) {
    try {
      // Format input - handle both string and array formats
      let formattedInput;
      if (Array.isArray(input)) {
        // If input is an array of competency gaps, format as a list
        formattedInput = input.join('\n');
      } else {
        // If input is a string, use as is
        formattedInput = input;
      }

      // Step 1: Run first prompt with input
      const fullPrompt1 = `${prompt1}\n\nInput: ${formattedInput}`;
      const output1 = await this.generateResponse(fullPrompt1);

      // Step 2: Run second prompt with output from step 1
      const fullPrompt2 = `${prompt2}\n\nInput: ${output1}`;
      const finalOutput = await this.generateResponse(fullPrompt2);

      return {
        step1: {
          prompt: fullPrompt1,
          output: output1
        },
        step2: {
          prompt: fullPrompt2,
          output: finalOutput
        },
        finalOutput
      };
    } catch (error) {
      console.error('Error in prompt chain:', error);
      throw error;
    }
  }
}

module.exports = new GeminiService();
